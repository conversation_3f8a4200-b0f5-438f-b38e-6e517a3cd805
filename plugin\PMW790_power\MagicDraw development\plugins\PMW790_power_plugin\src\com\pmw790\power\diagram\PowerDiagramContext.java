package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.pmw790.power.functions.Utilities;
import com.pmw790.power.functions.ConnectionRegistry;

import java.util.*;

/**
 * Abstract base class for power diagram contexts.
 * Contains shared functionality between RoomDiagramContext and CabinetDiagramContext.
 * This class manages common operations like property caching, constraint property management,
 * and value property handling.
 * Implements DiagramContext interface to provide standardized access for utility classes.
 */
public abstract class PowerDiagramContext implements DiagramContext {

    // Core shared fields
    protected final Project project;
    protected final ConnectionRegistry registry;
    protected final String contextName;

    // Shared caching structures
    protected final Map<String, Map<String, Property>> constraintPropertiesCache; // Element Name -> (Constraint Type -> Property)
    protected final Map<String, Map<String, Property>> valuePropertiesCache; // Element Name -> (Property Name -> Property)
    protected final Map<String, Property> externalLoadPropertyCache; // Element Name -> external_load Property

    /**
     * Constructor for the abstract base class
     *
     * @param project The MagicDraw project
     * @param registry The connection registry
     * @param contextName The name of this context (room name or cabinet name)
     */
    protected PowerDiagramContext(Project project, ConnectionRegistry registry, String contextName) {
        this.project = project;
        this.registry = registry;
        this.contextName = contextName;
        this.constraintPropertiesCache = new HashMap<>();
        this.valuePropertiesCache = new HashMap<>();
        this.externalLoadPropertyCache = new HashMap<>();
    }

    // Abstract methods that must be implemented by subclasses

    /**
     * Gets the main block element for this context (room block or cabinet block)
     * @return The block element
     */
    public abstract Class getContextBlock();

    /**
     * Gets the list of power providers in this context
     * @return List of provider names
     */
    public abstract List<String> getProviders();

    /**
     * Gets a part property by name from this context
     * @param propertyName The property name
     * @return The Property object, or null if not found
     */
    public abstract Property getPartProperty(String propertyName);

    // Additional abstract methods from DiagramContext interface

    /**
     * Gets consumers connected to a specific provider
     * @param providerName The provider name
     * @return List of consumer names
     */
    public abstract List<String> getConsumersForProvider(String providerName);

    /**
     * Checks if a provider is a top-level provider (has no parent providers)
     * @param providerName The provider name
     * @return true if this is a top-level provider
     */
    public abstract boolean isTopProvider(String providerName);

    /**
     * Gets child providers for a top provider
     * @param topProviderName The top provider name
     * @return List of child provider names
     */
    public abstract List<String> getChildProvidersForTop(String topProviderName);

    /**
     * Gets constraint ports for a specific constraint type of an element
     * @param elementName The element name
     * @param constraintType The constraint type name
     * @return Map of port name to Property object
     */
    public abstract Map<String, Property> getConstraintPorts(String elementName, String constraintType);

    /**
     * Gets the context type identifier
     * @return The context type (e.g., "CABINET", "ROOM")
     */
    public abstract String getContextType();

    /**
     * Checks if this context is operating within a room context
     * @return true if this is a room context or a cabinet within a room
     */
    public abstract boolean isRoomContext();

    // Shared getter methods

    /**
     * Gets the project
     * @return The MagicDraw project
     */
    public Project getProject() {
        return project;
    }

    /**
     * Gets the connection registry
     * @return The connection registry
     */
    public ConnectionRegistry getConnectionRegistry() {
        return registry;
    }

    /**
     * Gets the context name (room name or cabinet name)
     * @return The context name
     */
    public String getContextName() {
        return contextName;
    }

    // Shared constraint property management methods

    /**
     * Caches constraint properties for a specific element
     * @param elementName The element name (provider name)
     * @param constraintType The type of constraint (e.g., "Power_Total")
     * @param property The constraint property
     */
    public void cacheConstraintProperty(String elementName, String constraintType, Property property) {
        if (elementName != null && constraintType != null && property != null) {
            // Get or create the map for this element
            Map<String, Property> elementConstraints = constraintPropertiesCache.computeIfAbsent(
                elementName, k -> new HashMap<>());

            // Store the constraint property
            elementConstraints.put(constraintType, property);
        }
    }

    /**
     * Gets all constraint properties for a specific element
     * @param elementName The element name (provider name)
     * @return Map of constraint type to constraint property, or empty map if none found
     */
    public Map<String, Property> getConstraintProperties(String elementName) {
        return constraintPropertiesCache.getOrDefault(elementName, new HashMap<>());
    }

    /**
     * Default implementation for caching constraint ports
     * Subclasses can override for specialized behavior
     * @param elementName The element name
     * @param constraintType The constraint type
     * @param ports Map of port name to port property
     */
    @Override
    public void cacheConstraintPorts(String elementName, String constraintType, Map<String, Property> ports) {
        // Default implementation - subclasses should override if they have specialized constraint port caching
        Log("Default constraint ports caching for element: " + elementName + ", constraint: " + constraintType);
    }

    /**
     * Caches external load property for a specific element
     * @param elementName The element name (provider name)
     * @param property The external load property
     */
    public void cacheExternalLoadProperty(String elementName, Property property) {
        if (elementName != null && property != null) {
            externalLoadPropertyCache.put(elementName, property);
        }
    }

    /**
     * Gets external load property for a specific element
     * @param elementName The element name (provider name)
     * @return The external load property, or null if not found
     */
    public Property getExternalLoadProperty(String elementName) {
        return externalLoadPropertyCache.get(elementName);
    }

    // Shared value property management methods

    /**
     * Caches value properties for a specific element
     * @param elementName The element name
     * @param propertyName The property name
     * @param property The value property
     */
    public void cacheValueProperty(String elementName, String propertyName, Property property) {
        if (elementName != null && propertyName != null && property != null) {
            // Get or create the map for this element
            Map<String, Property> elementProperties = valuePropertiesCache.computeIfAbsent(
                elementName, k -> new HashMap<>());

            // Store the value property
            elementProperties.put(propertyName, property);
        }
    }

    /**
     * Gets all value properties for a specific element
     * @param elementName The element name
     * @return Map of property name to property, or empty map if none found
     */
    public Map<String, Property> getValueProperties(String elementName) {
        return valuePropertiesCache.getOrDefault(elementName, new HashMap<>());
    }

    /**
     * Default implementation for caching value properties
     * @param elementName The element name
     * @param valueProperties Map of property name to Property object
     */
    @Override
    public void cacheValueProperties(String elementName, Map<String, Property> valueProperties) {
        if (elementName != null && valueProperties != null && !valueProperties.isEmpty()) {
            valuePropertiesCache.put(elementName, new HashMap<>(valueProperties));
        }
    }

    /**
     * Gets a specific value property for an element
     * @param elementName The element name
     * @param propertyName The property name
     * @return The value property, or null if not found
     */
    public Property getValueProperty(String elementName, String propertyName) {
        Map<String, Property> elementProperties = valuePropertiesCache.get(elementName);
        if (elementProperties != null) {
            return elementProperties.get(propertyName);
        }
        return null;
    }

    // Shared utility methods

    /**
     * Logs a message with context information
     * @param message The message to log
     */
    protected void Log(String message) {
        Utilities.Log(getClass().getSimpleName() + " [" + contextName + "]: " + message);
    }

    /**
     * Creates a constraint property name for a given element and constraint type
     * @param elementName The element name
     * @param constraintType The constraint type
     * @return The constraint property name
     */
    protected String createConstraintPropertyName(String elementName, String constraintType) {
        return constraintType.toLowerCase() + "_" + elementName;
    }

    /**
     * Creates an external load property name for a given element
     * @param elementName The element name
     * @return The external load property name
     */
    protected String createExternalLoadPropertyName(String elementName) {
        return "external_load_" + elementName;
    }
}
